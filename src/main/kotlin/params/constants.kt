package params

import Story2C
import java.util.*
import kotlin.io.path.Path
import kotlin.io.path.absolutePathString
import kotlin.io.path.div

// PORTS:
val portWebApi = 8080
val portAdminWebApi = 8081
val portTestWebApi = 8082
val portWhinSelf = 8888
val portWhatsAppServer = portWhinSelf // Set to portTestWebApi for testing, 8888 for Whinself

// DB:
val DBTCPport = 8088
val DBDir =  Path(System.getProperty("user.home")) / "var/mochi"
val DBFile = (DBDir / "var/mochi/mochi.db").absolutePathString()
val DBName = "mochi.db"

// LLMS:
val LLMCacheSize = 10000
val anthropicModelId = "anthropic.claude-3-5-sonnet-20240620-v1:0"

// ENUMS
enum class MyLanguage(val code: String) {
    US_USA("US_USA"),       // deprecated, was a mistake
    EN_USA("EN_USA"),
    ES_Spain("ES_Spain"),
    FR_France("FR_France");

    fun llmExpression(): String {
        return when (this) {
            US_USA, EN_USA -> "English (US)"
            ES_Spain -> "Spanish (Spain)"
            FR_France -> "French (France)"
        }
    }
}

// LOGGING:
var debugPlanner: Boolean = false
var debugPrompt: Boolean = false


// STORIES:
val defaultLanguage = MyLanguage.US_USA
val defaultTimeZone = TimeZone.getTimeZone("CET")
val defaultStory = Story2C::class.simpleName!!


