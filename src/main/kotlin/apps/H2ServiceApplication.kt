package apps

import org.h2.tools.Server
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import params.DBDir
import params.DBTCPport
import java.sql.SQLException
import kotlin.io.path.absolutePathString

@SpringBootApplication
open class H2ServiceApplication

fun main(args: Array<String>) {
    System.setProperty("spring.main.web-application-type", "none")
    runApplication<H2ServiceApplication>(*args)
}

@Component
class H2ServerConfiguration {

    private lateinit var tcpServer: Server
    
    @EventListener(org.springframework.boot.context.event.ApplicationReadyEvent::class)
    fun startH2Server() {
        try {
            // Create database directory if it doesn't exist
            val dbDir = params.DBDir.toFile()
            if (!dbDir.exists()) {
                dbDir.mkdirs()
                println("Created database directory: ${dbDir.absolutePath}")
            }
            
            // Start TCP server with additional options to allow database creation
            tcpServer = Server.createTcpServer(
                "-tcp", 
                "-tcpAllowOthers", 
                "-tcpPort", "${params.DBTCPport}", 
                "-baseDir", dbDir.absolutePath,
                "-ifNotExists"  // This flag allows database creation if it doesn't exist
            ).start()
            println("H2 TCP Server started and listening on port ${params.DBTCPport}.")
            println("Database location: ${dbDir.absolutePath}")
        } catch (e: SQLException) {
            throw RuntimeException("Failed to start H2 server", e)
        }
    }

    @EventListener(org.springframework.context.event.ContextClosedEvent::class)
    fun stopH2Server() {
        println("Stopping H2 TCP Server...")
        tcpServer.stop()
        println("H2 TCP Server stopped.")
    }
}