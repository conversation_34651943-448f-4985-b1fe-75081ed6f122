package apps.adminweb_api

import apps.web_api.database.DatabaseConfig
import apps.web_api.database.GameStateRepository
import apps.web_api.database.Player
import apps.web_api.database.PlayerController
import io.github.oshai.kotlinlogging.KotlinLogging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import params.DBTCPport
import params.MyLanguage
import storyengine.AppResources
import storyengine.planner.GameState
import storyengine.planner.StoryManagerServerless
import utils.myTimeFormat

fun debugInfo(gameState: GameState): String {
    return listOf(
        "PLOT_NAME: ${gameState.workspace?.plot?.name}",
        "STARTED_GAMES: ${gameState.startedGames}",
        "LANG:${gameState.language.name}",
        "@Scene:${gameState.currentScene?.id}",
        "@PhType:${gameState.phase.type.name}",
        "@Round:${gameState.round}",
        "AwaitHumanInput:" + gameState.awaitingHumanInput()
    ).joinToString(" | ")
}

/**
 * Controller for admin pages
 */
class AdminController(
    private val playerController: PlayerController,
    private val gameStateRepository: GameStateRepository,
    private val appResources: AppResources
) {
    companion object {
        private val logger = KotlinLogging.logger {}
        private val json = Json {
            prettyPrint = true
            ignoreUnknownKeys = true
        }
    }

    @Serializable
    data class PlayerFormData(
        val id: String? = null,
        val phone: String = "",
        val name: String = "",
        val language: String = "",
        val timezone: String = "CET"
    )

    @Serializable
    data class GameStateSummary(
        val id: Int,
        val playerId: String,
        val gameId: String,
        val createdAt: String,
        val updatedAt: String,
        val debug: String? = null,
        val awaitingHumanInput: Boolean = false,
        val phase: String = "UNDEFINED",
        val status: String = "ACTIVE" // Add status field with default value
    )

    fun registerRoutes(routing: Routing) {
        routing.apply {
            // Admin dashboard
            get("/") {
                call.respond(FreeMarkerContent("admin/dashboard.ftl", mapOf("title" to "Admin Dashboard")))
            }
            get("/mochi/admin") {
                call.respond(FreeMarkerContent("admin/dashboard.ftl", mapOf("title" to "Admin Dashboard")))
            }

            // Database administration
            get("/mochi/admin/database") {
                call.respond(
                    FreeMarkerContent(
                        "admin/database/index.ftl", mapOf(
                            "title" to "Database Administration",
                            "databaseLocation" to DBTCPport.toString(),
                            "jdbcUrl" to DatabaseConfig.getJdbcUrl()
                        )
                    )
                )
            }

            // Reset database
            post("/mochi/admin/database/reset") {
                try {
                    val success = DatabaseConfig.resetDatabase()
                    if (success) {
                        call.respond(
                            FreeMarkerContent(
                                "admin/database/index.ftl", mapOf(
                                    "title" to "Database Administration",
                                    "databaseLocation" to DBTCPport.toString(),
                                    "jdbcUrl" to DatabaseConfig.getJdbcUrl(),
                                    "message" to "Database reset successfully"
                                )
                            )
                        )
                    } else {
                        call.respond(
                            FreeMarkerContent(
                                "admin/database/index.ftl", mapOf(
                                    "title" to "Database Administration",
                                    "databaseLocation" to DBTCPport.toString(),
                                    "jdbcUrl" to DatabaseConfig.getJdbcUrl(),
                                    "error" to "Failed to reset database"
                                )
                            )
                        )
                    }
                } catch (e: Exception) {
                    logger.error(e) { "Error resetting database" }
                    call.respond(
                        FreeMarkerContent(
                            "admin/database/index.ftl", mapOf(
                                "title" to "Database Administration",
                                "databaseLocation" to DBTCPport.toString(),
                                "jdbcUrl" to DatabaseConfig.getJdbcUrl(),
                                "error" to "Error resetting database: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Player management
            get("/mochi/admin/players") {
                val players = playerController.playerRepository.getAllPlayers()
                call.respond(
                    FreeMarkerContent(
                        "admin/players/list.ftl", mapOf(
                            "title" to "Player Management",
                            "players" to players
                        )
                    )
                )
            }

            // Create player form
            get("/mochi/admin/players/create") {
                call.respond(
                    FreeMarkerContent(
                        "admin/players/form.ftl", mapOf(
                            "title" to "Create Player",
                            "formAction" to "/mochi/admin/players/create",
                            "player" to PlayerFormData()
                        )
                    )
                )
            }

            // Process create player form
            post("/mochi/admin/players/create") {
                val formParameters = call.receiveParameters()
                val phone = formParameters["phone"] ?: ""
                val name = formParameters["name"] ?: ""
                val language = formParameters["language"] ?: "US_USA"
                val timezone = formParameters["timezone"] ?: "CET"

                try {
                    // Create player request
                    val request = PlayerController.CreatePlayerRequest(
                        phone = phone,
                        name = name,
                        language = language,
                        timezone = timezone
                    )

                    // Validate phone
                    if (request.phone.isBlank() || !request.phone.all { it.isDigit() || it == '+' }) {
                        return@post call.respond(
                            FreeMarkerContent(
                                "admin/players/form.ftl", mapOf(
                                    "title" to "Create Player",
                                    "formAction" to "/mochi/admin/players/create",
                                    "player" to PlayerFormData(
                                        phone = phone,
                                        name = name,
                                        language = language,
                                        timezone = timezone
                                    ),
                                    "error" to "Invalid phone number format"
                                )
                            )
                        )
                    }

                    // Check if phone exists
                    val existingPlayer = playerController.playerRepository.getPlayerByPhone(request.phone)
                    if (existingPlayer != null) {
                        return@post call.respond(
                            FreeMarkerContent(
                                "admin/players/form.ftl", mapOf(
                                    "title" to "Create Player",
                                    "formAction" to "/mochi/admin/players/create",
                                    "player" to PlayerFormData(
                                        phone = phone,
                                        name = name,
                                        language = language,
                                        timezone = timezone
                                    ),
                                    "error" to "A player with this phone number already exists"
                                )
                            )
                        )
                    }
                    // Create new player
                    val player = Player(
                        id = "0",
                        phone = request.phone,
                        name = request.name,
                        language = request.language,
                        timezone = request.timezone
                    )
                    playerController.playerRepository.savePlayer(player)

                    // Redirect to player list
                    call.respondRedirect("/mochi/admin/players")
                } catch (e: Exception) {
                    logger.error(e) { "Error creating player" }
                    call.respond(
                        FreeMarkerContent(
                            "admin/players/form.ftl", mapOf(
                                "title" to "Create Player",
                                "formAction" to "/mochi/admin/players/create",
                                "player" to PlayerFormData(
                                    phone = phone,
                                    name = name,
                                    language = language,
                                    timezone = timezone
                                ),
                                "error" to "Error creating player: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Edit player form
            get("/mochi/admin/players/edit/{playerId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val player : Player = playerController.playerRepository.getPlayer(playerId)
                    ?: return@get call.respond(HttpStatusCode.NotFound)

                call.respond(
                    FreeMarkerContent(
                        "admin/players/form.ftl", mapOf(
                            "title" to "Edit Player",
                            "formAction" to "/mochi/admin/players/edit/$playerId",
                            "player" to PlayerFormData(
                                id = player.id,
                                phone = player.phone,
                                name = player.name,
                                language = player.language
                            )
                        )
                    )
                )
            }

            // Process edit player form
            post("/mochi/admin/players/edit/{playerId}") {
                val playerId = call.parameters["playerId"] ?: return@post call.respond(HttpStatusCode.BadRequest)
                val formParameters = call.receiveParameters()
                val phone = formParameters["phone"] ?: ""
                val name = formParameters["name"] ?: ""
                val language = formParameters["language"] ?: "US_USA"

                try {
                    // Get existing player
                    val existingPlayer = playerController.playerRepository.getPlayer(playerId)
                        ?: return@post call.respond(HttpStatusCode.NotFound)

                    // Validate phone
                    if (phone.isBlank() || !phone.all { it.isDigit() || it == '+' }) {
                        return@post call.respond(
                            FreeMarkerContent(
                                "admin/players/form.ftl", mapOf(
                                    "title" to "Edit Player",
                                    "formAction" to "/mochi/admin/players/edit/$playerId",
                                    "player" to PlayerFormData(
                                        id = playerId,
                                        phone = phone,
                                        name = name,
                                        language = language
                                    ),
                                    "error" to "Invalid phone number format"
                                )
                            )
                        )
                    }

                    // Check if phone exists (only if changed)
                    if (phone != existingPlayer.phone) {
                        val phoneExists = playerController.playerRepository.getPlayerByPhone(phone)
                        if (phoneExists != null) {
                            return@post call.respond(
                                FreeMarkerContent(
                                    "admin/players/form.ftl", mapOf(
                                        "title" to "Edit Player",
                                        "formAction" to "/mochi/admin/players/edit/$playerId",
                                        "player" to PlayerFormData(
                                            id = playerId,
                                            phone = phone,
                                            name = name,
                                            language = language
                                        ),
                                        "error" to "A player with this phone number already exists"
                                    )
                                )
                            )
                        }
                    }

                    // Update player
                    val updatedPlayer = existingPlayer.copy(
                        phone = phone,
                        name = name,
                        language = language
                    )
                    playerController.playerRepository.updatePlayer(updatedPlayer)

                    // Redirect to player list
                    call.respondRedirect("/mochi/admin/players")
                } catch (e: Exception) {
                    logger.error(e) { "Error updating player" }
                    call.respond(
                        FreeMarkerContent(
                            "admin/players/form.ftl", mapOf(
                                "title" to "Edit Player",
                                "formAction" to "/mochi/admin/players/edit/$playerId",
                                "player" to PlayerFormData(
                                    id = playerId,
                                    phone = phone,
                                    name = name,
                                    language = language
                                ),
                                "error" to "Error updating player: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Delete player
            get("/mochi/admin/players/delete/{playerId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                try {
                    playerController.playerRepository.deletePlayer(playerId)
                    call.respondRedirect("/mochi/admin/players")
                } catch (e: Exception) {
                    logger.error(e) { "Error deleting player" }
                    call.respondRedirect("/mochi/admin/players?error=Error+deleting+player:+${e.message}")
                }
            }

            // Game States management - List all game states
            get("/mochi/admin/gamestates") {
                try {
                    val allGameStates = gameStateRepository.getAllGameStates()
                    logger.info { "Retrieved ${allGameStates.size} game states from repository" }
                    
                    val gameStates = allGameStates.mapNotNull { gameStateRecord ->
                        try {
                            // Extract basic info from game state JSON
                            val gameState = GameState.fromJson(gameStateRecord.gameState)
                            
                            GameStateSummary(
                                id = gameStateRecord.id,
                                playerId = gameStateRecord.playerId,
                                gameId = gameStateRecord.gameId,
                                createdAt = myTimeFormat(gameStateRecord.createdAt),
                                updatedAt = myTimeFormat(gameStateRecord.updatedAt),
                                debug = debugInfo(gameState),
                                awaitingHumanInput = gameState.awaitingHumanInput(),
                                phase = gameState.phase.type.name,
                                status = gameStateRecord.status // Add the raw status from DB
                            )
                        } catch (e: Exception) {
                            logger.error(e) { "Error parsing game state JSON for record ID ${gameStateRecord.id}" }
                            null // Skip this record if we can't parse it
                        }
                    }
                    
                    logger.info { "Successfully processed ${gameStates.size} game states for display" }

                    call.respond(
                        FreeMarkerContent(
                            "admin/gamestates/list.ftl", mapOf(
                                "title" to "Game States Management",
                                "gameStates" to gameStates
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error(e) { "Error retrieving game states" }
                    call.respond(
                        FreeMarkerContent(
                            "admin/gamestates/list.ftl", mapOf(
                                "title" to "Game States Management",
                                "gameStates" to emptyList<GameStateSummary>(),
                                "error" to "Error retrieving game states: ${e.message}"
                            )
                        )
                    )
                }
            }

            // View game state details
            get("/mochi/admin/gamestates/view/{playerId}/{gameId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val gameId = call.parameters["gameId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                val gameStateJson = gameStateRepository.getGameState(playerId, gameId)
                    ?: return@get call.respond(HttpStatusCode.NotFound, "Game state not found")

                // Parse the game state
                val gameState = try {
                    GameState.fromJson(gameStateJson)
                } catch (e: Exception) {
                    logger.error(e) { "Error parsing game state" }
                    return@get call.respond(
                        HttpStatusCode.InternalServerError,
                        "Error parsing game state: ${e.message}"
                    )
                }

                // Format the JSON for display
                val formattedJson = try {
                    json.parseToJsonElement(gameStateJson).toString()
                } catch (e: Exception) {
                    gameStateJson
                }

                // Extract debug info as a string instead of a map
                val debugInfoString = debugInfo(gameState)

                // Extract and format the dialogue
                val dialogueHtml = try {
                    val workspace = gameState.workspace
                    val dialogue = workspace?.dialogue

                    if (dialogue != null) {
                        val turns = dialogue.turns
                        val times = dialogue.times

                        if (turns.isNotEmpty() && turns.size == times.size) {
                            val dialogueLines = turns.mapIndexed { index, turn ->
                                val (speaker, message, listener) = turn
                                val time = times[index]

                                val timeStr = if (time != null) {
                                    val dateTime =
                                        java.time.LocalDateTime.ofEpochSecond(time, 0, java.time.ZoneOffset.UTC)
                                    val formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                                    dateTime.format(formatter)
                                } else {
                                    ""
                                }

                                "<tr>" +
                                        "<td>$timeStr</td>" +
                                        "<td>$speaker</td>" +
                                        "<td>$message</td>" +
                                        "<td>$listener</td>" +
                                        "</tr>"
                            }

                            "<table class='table table-striped'>" +
                                    "<thead><tr><th>Time</th><th>Speaker</th><th>Message</th><th>Listener</th></tr></thead>" +
                                    "<tbody>${dialogueLines.joinToString("")}</tbody>" +
                                    "</table>"
                        } else {
                            "<p>No dialogue found or dialogue format is invalid.</p>"
                        }
                    } else {
                        "<p>No dialogue found in game state.</p>"
                    }
                } catch (e: Exception) {
                    logger.error(e) { "Error extracting dialogue" }
                    "<p>Error extracting dialogue: ${e.message}</p>"
                }

                // Get the prompt logger file path
                val promptLoggerFile = appResources.promptLogFile(gameId)
                val promptLoggerFilePath = promptLoggerFile.absolutePath
                val promptLoggerFileExists = promptLoggerFile.exists()

                call.respond(
                    FreeMarkerContent(
                        "admin/gamestates/view.ftl", mapOf(
                            "title" to "Game State Details",
                            "playerId" to playerId,
                            "gameId" to gameId,
                            "gameStateJson" to formattedJson,
                            "dialogueHtml" to dialogueHtml,
                            "debugInfoString" to debugInfoString,
                            "promptLoggerFilePath" to promptLoggerFilePath,
                            "promptLoggerFileExists" to promptLoggerFileExists
                        )
                    )
                )
            }

            // Delete game state
            get("/mochi/admin/gamestates/delete/{playerId}/{gameId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val gameId = call.parameters["gameId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                try {
                    val deleted = gameStateRepository.deleteGameState(playerId, gameId)
                    if (deleted) {
                        call.respondRedirect("/mochi/admin/gamestates")
                    } else {
                        call.respond(HttpStatusCode.NotFound, "Game state not found")
                    }
                } catch (e: Exception) {
                    logger.error(e) { "Error deleting game state" }
                    call.respondRedirect("/mochi/admin/gamestates?error=Error+deleting+game+state:+${e.message}")
                }
            }

            // Create new game form
            get("/mochi/admin/gamestates/create") {
                // Get all players for the dropdown
                val players = playerController.playerRepository.getAllPlayers()

                call.respond(
                    FreeMarkerContent(
                        "admin/gamestates/create.ftl", mapOf(
                            "title" to "Create New Game",
                            "players" to players
                        )
                    )
                )
            }

            // Process create game form
            post("/mochi/admin/gamestates/create") {
                val formParameters = call.receiveParameters()
                val playerId = formParameters["playerId"] ?: ""

                val storyModuleName = formParameters["storyModuleName"] ?: "Story2C"
                val language = formParameters["language"] ?: "US_USA"

                try {
                    // Validate inputs
                    if (playerId.isBlank()) {
                        return@post call.respond(
                            FreeMarkerContent(
                                "admin/gamestates/create.ftl", mapOf(
                                    "title" to "Create New Game",
                                    "players" to playerController.playerRepository.getAllPlayers(),
                                    "error" to "Player ID is required"
                                )
                            )
                        )
                    }

                    // Check if player exists
                    val player = playerController.playerRepository.getPlayer(playerId)
                    if (player == null) {
                        return@post call.respond(
                            FreeMarkerContent(
                                "admin/gamestates/create.ftl", mapOf(
                                    "title" to "Create New Game",
                                    "players" to playerController.playerRepository.getAllPlayers(),
                                    "error" to "Selected player does not exist"
                                )
                            )
                        )
                    }

                    // Create new game state
                    val newGameState = GameState.newGame(
                        storyModuleName = storyModuleName,
                        language = MyLanguage.valueOf(language)
                    )
                    val newGameId = "0" // autoincrement

                    // Log the game ID being used
                    logger.info { "Creating new game with ID: $newGameId for player: $playerId" }

                    // Save game state
                    gameStateRepository.saveGameState(playerId, newGameId, newGameState.toJson())

                    // Redirect to game states list
                    call.respondRedirect("/mochi/admin/gamestates")

                } catch (e: Exception) {
                    logger.error(e) { "Error creating game state" }
                    call.respond(
                        FreeMarkerContent(
                            "admin/gamestates/create.ftl", mapOf(
                                "title" to "Create New Game",
                                "players" to playerController.playerRepository.getAllPlayers(),
                                "error" to "Error creating game: ${e.message}"
                            )
                        )
                    )
                }
            }

            // Step game state
            get("/mochi/admin/gamestates/step/{playerId}/{gameId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val gameId = call.parameters["gameId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                try {
                    // Load game state
                    val gameStateJson = gameStateRepository.getGameState(playerId, gameId)
                        ?: return@get call.respond(HttpStatusCode.NotFound, "Game state not found")

                    // Parse game state
                    val gameState = GameState.fromJson(gameStateJson)

                    // Set up story manager
                    val sms = StoryManagerServerless(appResources, null, null)

                    // Run a single game loop iteration
                    withContext(Dispatchers.IO) {
                        val gameStepController = GameStepController(appResources)
                        gameStepController.runGameStep(gameState, sms)
                    }

                    // Save updated game state
                    val updatedGameStateJson = gameState.toJson()
                    gameStateRepository.saveGameState(playerId, gameId, updatedGameStateJson)

                    // Redirect back to the game states list with success message
                    call.respondRedirect("/mochi/admin/gamestates?message=Game+stepped+successfully")
                } catch (e: Exception) {
                    logger.error(e) { "Error stepping game state" }
                    call.respondRedirect("/mochi/admin/gamestates?error=Error+stepping+game:+${e.message}")
                }
            }

            // View raw game state JSON
            get("/mochi/admin/gamestates/json/{playerId}/{gameId}") {
                val playerId = call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val gameId = call.parameters["gameId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                val gameStateJson = gameStateRepository.getGameState(playerId, gameId)
                    ?: return@get call.respond(HttpStatusCode.NotFound, "Game state not found")

                // Pretty print the JSON
                val prettyJson = try {
                    val jsonElement = json.parseToJsonElement(gameStateJson)
                    json.encodeToString(jsonElement)
                } catch (e: Exception) {
                    logger.error(e) { "Error formatting JSON" }
                    gameStateJson // Fallback to the original JSON if formatting fails
                }

                call.respond(
                    FreeMarkerContent(
                        "admin/gamestates/json.ftl", mapOf(
                            "title" to "Game State Raw JSON",
                            "playerId" to playerId,
                            "gameId" to gameId,
                            "rawJson" to prettyJson
                        )
                    )
                )
            }

            // View prompt logger file
            get("/mochi/admin/gamestates/promptlog/{playerId}/{gameId}") {
                call.parameters["playerId"] ?: return@get call.respond(HttpStatusCode.BadRequest)
                val gameId = call.parameters["gameId"] ?: return@get call.respond(HttpStatusCode.BadRequest)

                val promptLoggerFile = appResources.promptLogFile(gameId)

                if (!promptLoggerFile.exists()) {
                    return@get call.respond(HttpStatusCode.NotFound, "Prompt logger file not found")
                }

                // Read the file content
                val fileContent = promptLoggerFile.readText()

                // Respond with the file content as HTML
                call.respondText(fileContent, ContentType.Text.Html)
            }
        }
    }
}
