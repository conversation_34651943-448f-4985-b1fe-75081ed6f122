package apps.web_api

import apps.web_api.database.PlayerController
import apps.web_api.database.DatabaseConfig
import apps.web_api.database.GameStateRepository
import freemarker.cache.ClassTemplateLoader
import io.github.oshai.kotlinlogging.KotlinLogging
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.freemarker.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import params.portWebApi
import storyengine.AppResources

class WebApi(val appResources: AppResources) {
    companion object {
        val logger = KotlinLogging.logger {}
    }


    @Serializable
    data class GameStateSummary(
        val id: Int,
        val playerId: String,
        val gameId: String,
        val createdAt: String,
        val updatedAt: String
    )

    @Serializable
    data class CreateGameRequest(
        val storyModuleName: String = "Story2C",
        val language: String = "US_USA",
    )

    fun start(port: Int = portWebApi) {
        logger.info { "Starting MochiWebApi on port $port" }

        // Initialize database with consistent connection
        DatabaseConfig.initDatabase()

        // Initialize controllers
        val appRes = AppResources()
        val playerController = PlayerController()
        val gameStateRepository = GameStateRepository()
        val commandController =
            CommandController(appRes, playerController.playerRepository, gameStateRepository)  // Pass both repositories


        embeddedServer(Netty, port = port) {
            // Install FreeMarker feature
            install(FreeMarker) {
                templateLoader = ClassTemplateLoader(this::class.java.classLoader, "templates")
            }

            // Add ContentNegotiation feature
            install(ContentNegotiation) {
                json(Json {
                    prettyPrint = true
                    isLenient = true
                    ignoreUnknownKeys = true
                })
            }

            routing {
                commandController.registerRoutes(this)

                // Test endpoint
                get("/mochi/test") {
                    call.respondText("hello", ContentType.Text.Plain)
                }

            }
        }.start(wait = true)
    }
}