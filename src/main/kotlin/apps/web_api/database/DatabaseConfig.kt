package apps.web_api.database

import io.github.oshai.kotlinlogging.KotlinLogging
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import params.DBDir
import params.DBName
import params.DBTCPport


object DatabaseConfig {
    private val logger = KotlinLogging.logger {}
    // Fix the JDBC URL - remove AUTO_SERVER=TRUE since we're using TCP mode
    val DEFAULT_JDBC_URL = "jdbc:h2:tcp://localhost:${DBTCPport}/${DBDir.toFile().absolutePath}/${DBName};DB_CLOSE_ON_EXIT=FALSE"
    const val DB_DRIVER = "org.h2.Driver"

    private var initialized = false
    private var database: Database? = null

    /**
     * Initialize the database connection and create tables if needed
     * @param jdbcUrl The JDBC URL to connect to
     * @param driver The JDBC driver to use
     * @return The Database instance
     */
    fun initDatabase(jdbcUrl: String = DEFAULT_JDBC_URL, driver: String = DB_DRIVER): Database {
        if (initialized && database != null) {
            return database!!
        }

        // Ensure database directory exists
        val dbDir = DBDir.toFile()
        if (!dbDir.exists()) {
            dbDir.mkdirs()
            logger.info { "Created database directory: ${dbDir.absolutePath}" }
        }

        // Connect to the database
        logger.info{"Connecting to database: $jdbcUrl"}
        try {
            database = Database.connect(url = jdbcUrl, driver = driver)
        } catch (e: Exception) {
            logger.error(e) { "Error connecting to database: $jdbcUrl" }
            throw e
        }

        // Create all tables that need creation
        transaction {
            SchemaUtils.create(Players, GameStates)
        }
        initialized = true
        return database!!
    }

    /**
     * Get the JDBC URL to use for database connections
     * @return The JDBC URL
     */
    fun getJdbcUrl(): String {
        return DEFAULT_JDBC_URL
    }


    /**
     * Get the current database connection, initializing it if necessary
     * @return The Database instance
     */
    fun getDatabase(): Database {
        return database ?: initDatabase()
    }

    /**
     * Reset the database by dropping all tables and recreating them
     * @return True if the reset was successful
     */
    fun resetDatabase(): Boolean {
        logger.warn { "Resetting database - all data will be lost!" }

        try {
            transaction {
                // Drop all tables
                SchemaUtils.drop(
                    Players,
                    GameStates
                    // Add any other tables here
                )

                // Recreate all tables
                SchemaUtils.create(
                    Players,
                    GameStates
                    // Add any other tables here
                )
            }

            logger.info { "Database reset successfully" }
            return true
        } catch (e: Exception) {
            logger.error(e) { "Error resetting database" }
            return false
        }
    }

}

